{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:205:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:51:22'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:205:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:52:14'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:52:23'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:53:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 13:10:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 13:11:20'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 13:13:53'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:47'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:06'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:07'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:08'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-10 13:15:08'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:43:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:43:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:43:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:43:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:04'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:57'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:58'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:45:00'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:45:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: 'Port 3000 is already in use',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:57:35'
}
{
  message: 'Port 3000 is already in use',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:15:19'
}
{
  message: 'Port 3000 is already in use',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:21:17'
}
{
  message: 'Port 3000 is already in use',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:01:43'
}
{
  message: 'Port 3000 is already in use',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:20:47'
}
{
  message: 'Port 3000 is already in use',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:32:15'
}
{
  message: 'Port 3000 is already in use',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:55:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:31'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:31'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:31'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:33'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:33'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:34'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:35'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:36'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:37'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:38'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:38'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-13 01:23:38'
}
{
  message: 'Port 3000 is already in use',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:24:29'
}
{
  message: 'Port 3000 is already in use',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:32:57'
}
{
  message: 'Port 3000 is already in use',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:33:44'
}
{
  message: 'Port 3000 is already in use',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:39'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:39'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:39'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:39'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:40'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:40'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:41'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:41'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:42'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:43'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:44'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:45'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:45'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-15 01:20:45'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:57'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:58'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:58'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:59'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-15 01:21:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:20'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:20'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:20'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:20'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:20'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:21'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:21'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:22'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:22'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:23'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:24'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:25'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:25'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-15 01:21:25'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:33'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:33'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:34'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:35'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:35'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:36'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:37'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:37'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-15 01:21:37'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:48'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:48'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:48'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:48'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:49'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:49'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:50'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:50'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:51'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:52'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:53'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:54'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:54'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-15 01:21:54'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:06'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:07'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:08'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:09'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:09'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-15 01:22:09'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:06'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:07'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:07'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-18 19:15:07'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column expert_profiles_1.bio does not exist',
  level: 'error',
  timestamp: '2025-06-18 19:15:47'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 19:15:47',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:06'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:07'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:08'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:08'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-18 19:31:08'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column expert_profiles_1.bio does not exist',
  level: 'error',
  timestamp: '2025-06-18 19:32:35'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 19:32:35',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column expert_profiles_1.bio does not exist',
  level: 'error',
  timestamp: '2025-06-18 20:10:02'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 20:10:02',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column expert_profiles_1.bio does not exist',
  level: 'error',
  timestamp: '2025-06-18 20:11:53'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 20:11:53',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column expert_profiles_1.total_reviews does not exist',
  level: 'error',
  timestamp: '2025-06-18 20:19:35'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 20:19:35',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column expert_profiles_1.total_reviews does not exist',
  level: 'error',
  timestamp: '2025-06-18 21:20:25'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 21:20:25',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column services.is_active does not exist',
  level: 'error',
  timestamp: '2025-06-18 21:24:29'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 21:24:29',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching experts: column expert_profiles.is_available does not exist',
  level: 'error',
  timestamp: '2025-06-18 21:35:25'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 21:35:25',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search experts error: Failed to search experts',
  stack: 'AppError: Failed to search experts\n' +
    '    at searchExperts (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\experts.ts:288:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '22P02',
  details: 'Token "AVAILABLE" is invalid.',
  hint: null,
  message: 'Error searching experts: invalid input syntax for type json',
  level: 'error',
  timestamp: '2025-06-18 21:37:27'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 21:37:27',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search experts error: Failed to search experts',
  stack: 'AppError: Failed to search experts\n' +
    '    at searchExperts (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\experts.ts:292:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '22P02',
  details: 'Token "AVAILABLE" is invalid.',
  hint: null,
  message: 'Error searching experts: invalid input syntax for type json',
  level: 'error',
  timestamp: '2025-06-18 21:52:20'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 21:52:20',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search experts error: Failed to search experts',
  stack: 'AppError: Failed to search experts\n' +
    '    at searchExperts (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\experts.ts:292:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '22P02',
  details: 'Token "AVAILABLE" is invalid.',
  hint: null,
  message: 'Error searching experts: invalid input syntax for type json',
  level: 'error',
  timestamp: '2025-06-18 22:03:25'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 22:03:25',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search experts error: Failed to search experts',
  stack: 'AppError: Failed to search experts\n' +
    '    at searchExperts (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\experts.ts:291:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '22P02',
  details: 'Token "AVAILABLE" is invalid.',
  hint: null,
  message: 'Error searching experts: invalid input syntax for type json',
  level: 'error',
  timestamp: '2025-06-18 22:08:24'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 22:08:24',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search experts error: Failed to search experts',
  stack: 'AppError: Failed to search experts\n' +
    '    at searchExperts (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\experts.ts:292:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '22P02',
  details: 'Token "AVAILABLE" is invalid.',
  hint: null,
  message: 'Error searching experts: invalid input syntax for type json',
  level: 'error',
  timestamp: '2025-06-18 22:09:40'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 22:09:40',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search experts error: Failed to search experts',
  stack: 'AppError: Failed to search experts\n' +
    '    at searchExperts (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\experts.ts:292:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  message: "Application Error Cannot read properties of undefined (reading 'findUserByEmail')",
  stack: "TypeError: Cannot read properties of undefined (reading 'findUserByEmail')\n" +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\auth.ts:95:32)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\utils\\asyncHandler.ts:18:21)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\middleware\\validation.ts:46:7)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5',
  statusCode: 500,
  code: 'INTERNAL_ERROR',
  path: '/api/v1/auth/login',
  method: 'POST',
  userId: undefined,
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  level: 'error',
  timestamp: '2025-06-19 00:46:08'
}
{
  service: 'freela-api',
  environment: 'development',
  message: "Application Error Cannot read properties of undefined (reading 'findUserByEmail')",
  stack: "TypeError: Cannot read properties of undefined (reading 'findUserByEmail')\n" +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\auth.ts:95:32)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\utils\\asyncHandler.ts:18:21)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\middleware\\validation.ts:46:7)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5',
  statusCode: 500,
  code: 'INTERNAL_ERROR',
  path: '/api/v1/auth/login',
  method: 'POST',
  userId: undefined,
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  level: 'error',
  timestamp: '2025-06-19 00:47:33'
}
{
  service: 'freela-api',
  environment: 'development',
  message: "Application Error Cannot read properties of undefined (reading 'findUserByEmail')",
  stack: "TypeError: Cannot read properties of undefined (reading 'findUserByEmail')\n" +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\auth.ts:95:32)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\utils\\asyncHandler.ts:18:21)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\middleware\\validation.ts:46:7)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5',
  statusCode: 500,
  code: 'INTERNAL_ERROR',
  path: '/api/v1/auth/login',
  method: 'POST',
  userId: undefined,
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  level: 'error',
  timestamp: '2025-06-19 00:48:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'listen EADDRINUSE: address already in use 0.0.0.0:3001',
  code: 'EADDRINUSE',
  level: 'error',
  message: 'Server error occurred',
  timestamp: '2025-06-19 04:20:15'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'listen EADDRINUSE: address already in use 0.0.0.0:3001',
  code: 'EADDRINUSE',
  level: 'error',
  message: 'Server error occurred',
  timestamp: '2025-06-19 05:25:19'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'listen EADDRINUSE: address already in use 0.0.0.0:3005',
  code: 'EADDRINUSE',
  level: 'error',
  message: 'Server error occurred',
  timestamp: '2025-06-20 04:42:19'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'listen EADDRINUSE: address already in use 0.0.0.0:3005',
  code: 'EADDRINUSE',
  level: 'error',
  message: 'Server error occurred',
  timestamp: '2025-06-20 04:46:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'listen EADDRINUSE: address already in use 0.0.0.0:3005',
  code: 'EADDRINUSE',
  level: 'error',
  message: 'Server error occurred',
  timestamp: '2025-06-20 04:46:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'listen EADDRINUSE: address already in use 0.0.0.0:3005',
  code: 'EADDRINUSE',
  level: 'error',
  message: 'Server error occurred',
  timestamp: '2025-06-20 13:31:51'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'XgZhq8dLEUkOOO1A3qEt-',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-20 18:54:30'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: '5BdccYyH5aZIQBlZ-LH6J',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-20 18:59:41'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'q8MW7Qf2nBUdiwCzhlZJs',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-21 04:50:15'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'ECl7gSwTjoZdQDzOpxDdV',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-21 04:50:31'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'D5QIvX-x_Qt0GZg-LL9Ko',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-21 04:51:24'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'k_GyGA-bIrKqAI46OtQN5',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-21 04:51:27'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: '4JGON_Cl6fWR0WrD2og6S',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-21 04:53:45'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'IAIuiipI-5VoHt5PxS_ZV',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-21 04:53:52'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'E674yjrXr3t1nz6RjiGka',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-21 04:54:15'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'r4zaiio7AEefR11_aYgfo',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-21 05:03:51'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'j2c9LiIxIqO1veVss4Jdp',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-21 05:05:14'
}
{
  service: 'freela-api',
  environment: 'development',
  error: RangeError: Invalid time value
      at Date.toISOString (<anonymous>)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:74:59)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:504:35)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: 'Fr2e8MVysQwWngNYCQ775',
  sessionType: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-21 05:06:51'
}
{
  service: 'freela-api',
  environment: 'development',
  error: RangeError: Invalid time value
      at Date.toISOString (<anonymous>)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:74:59)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:504:35)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: 'j768Z7Hs32g4oXBQDoC8O',
  sessionType: 'user_dGVzdEBleGFtcGxl',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-21 05:06:51'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'Fr2e8MVysQwWngNYCQ775',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-21 05:06:51'
}
{
  service: 'freela-api',
  environment: 'development',
  error: RangeError: Invalid time value
      at Date.toISOString (<anonymous>)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:74:59)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:504:35)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: 'fW2Hx5yh4dzJGlTpWpWeW',
  sessionType: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-21 05:07:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'fW2Hx5yh4dzJGlTpWpWeW',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-21 05:07:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: RangeError: Invalid time value
      at Date.toISOString (<anonymous>)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:74:59)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:504:35)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: 'Uyq5TFkWF79YLJxFHQX5x',
  sessionType: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-21 05:07:15'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:367:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'Uyq5TFkWF79YLJxFHQX5x',
  userId: 'user_YW1lcmthbGxham9v',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-21 05:07:15'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.storeInDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:324:8)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:91:40)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:487:51)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: '7c4ab397-8da0-4245-8449-4fc1cce0f795',
  sessionType: 'auth',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-22 22:35:13'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.storeInDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:324:8)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:91:40)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:511:51)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: 'df5cf7ec-3191-4e86-a0a0-91eefa6fa07c',
  sessionType: 'auth',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-22 22:38:17'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.storeInDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:324:8)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:91:40)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:511:51)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: '7cdc6559-ff8b-4480-a9a7-3fe9877b6dac',
  sessionType: 'auth',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-22 22:39:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.storeInDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:324:8)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:91:40)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:511:51)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: 'cf470016-27e0-489d-bc08-0e699cceec71',
  sessionType: 'auth',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-22 22:40:41'
}
{
  service: 'freela-api',
  environment: 'development',
  error: ReferenceError: supabase is not defined
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:375:17)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:120:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'nkUJErbAu_0UrXFHwdKiy',
  userId: 'cf470016-27e0-489d-bc08-0e699cceec71',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-22 22:40:41'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.storeInDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:325:8)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:92:40)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:511:51)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: '5e46c423-bf29-48f0-94a5-baa56e5ce0f9',
  sessionType: 'auth',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-22 22:47:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.storeInDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:325:8)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:92:40)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:511:51)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: '0618b8a0-9282-43d0-a72b-cc701ff0a899',
  sessionType: 'auth',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-22 22:48:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:377:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'prczesaNxF-zgf5GFO7dP',
  userId: '0618b8a0-9282-43d0-a72b-cc701ff0a899',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-22 22:48:15'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.storeInDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:325:8)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:92:40)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:511:51)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: 'f72f09aa-c90b-4393-a89b-d51d1c8dea10',
  sessionType: 'auth',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-22 22:51:12'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:377:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'R4JkouMUI3Iewm64pJYMU',
  userId: 'f72f09aa-c90b-4393-a89b-d51d1c8dea10',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-22 22:51:20'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.storeInDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:325:8)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:92:40)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:511:51)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: 'c02025dd-6cfd-424b-ab2f-db9fec00adcf',
  sessionType: 'auth',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-22 22:51:54'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.storeInDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:325:8)
      at UnifiedSessionManager.createSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:92:40)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\controllers\auth.ts:511:51)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\utils\asyncHandler.ts:18:21)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\validation.ts:46:7)
      at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:149:13)
      at Route.dispatch (C:\Users\<USER>\Documents\Freela\node_modules\express\lib\router\route.js:119:3),
  userId: 'fd4c5a97-79bf-46c2-ad8e-2d5f2eb7aca6',
  sessionType: 'auth',
  level: 'error',
  message: 'Failed to create unified session',
  timestamp: '2025-06-23 19:54:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: TypeError: Cannot read properties of undefined (reading 'from')
      at UnifiedSessionManager.getFromDatabase (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:377:8)
      at UnifiedSessionManager.getSession (C:\Users\<USER>\Documents\Freela\apps\api\src\services\unifiedSessionManager.ts:121:30)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async authenticate (C:\Users\<USER>\Documents\Freela\apps\api\src\middleware\auth.ts:103:28),
  sessionId: 'x2X_2-eAA8zZ0jTx6t2Kb',
  userId: 'fd4c5a97-79bf-46c2-ad8e-2d5f2eb7aca6',
  level: 'error',
  message: 'Failed to get session',
  timestamp: '2025-06-23 19:55:00'
}
