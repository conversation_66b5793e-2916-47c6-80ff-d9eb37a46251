/**
 * Unified Session Management Service
 * Consolidates AI conversation sessions, authentication sessions, and database persistence
 * Resolves SESS-001: Session management conflicts between in-memory, Redis, and Supabase
 */

import { nanoid } from 'nanoid';
import { supabaseAdmin } from '@freela/database';
import { sessionHelpers, fallbackSessionHelpers } from '../utils/redis';
import { logger } from '../utils/logger';
import { createError } from '../utils/errors';

// Unified session interface
export interface UnifiedSession {
  id: string;
  userId: string;
  sessionType: 'auth' | 'ai_conversation' | 'ai_onboarding';
  status: 'active' | 'completed' | 'expired' | 'abandoned';
  data: Record<string, any>;
  metadata: {
    userRole?: 'CLIENT' | 'EXPERT' | 'ADMIN';
    language?: 'ar' | 'en';
    userAgent?: string;
    ipAddress?: string;
    lastActivity?: string;
  };
  createdAt: string;
  expiresAt: string;
  updatedAt: string;
}

// AI-specific session data structure
export interface AISessionData {
  sessionType: 'onboarding' | 'profile_optimization' | 'service_creation';
  userRole: 'CLIENT' | 'EXPERT';
  language: 'ar' | 'en';
  currentStep: string;
  messages: Array<{
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    contentArabic?: string;
    timestamp: string;
    metadata?: Record<string, any>;
  }>;
  extractedData: Record<string, any>;
  recommendations: any[];
  culturalContext?: Record<string, any>;
  aiModel?: string;
  completionRate?: number;
  totalSteps?: number;
}

class UnifiedSessionManager {
  private readonly SESSION_TTL = {
    auth: 7 * 24 * 60 * 60, // 7 days
    ai_conversation: 24 * 60 * 60, // 24 hours
    ai_onboarding: 7 * 24 * 60 * 60, // 7 days
  };

  /**
   * Create a new unified session
   */
  async createSession(
    userId: string,
    sessionType: 'auth' | 'ai_conversation' | 'ai_onboarding',
    data: Record<string, any> = {},
    metadata: UnifiedSession['metadata'] = {}
  ): Promise<UnifiedSession> {
    try {
      const now = new Date().toISOString();
      const ttl = this.SESSION_TTL[sessionType];
      const expiresAt = new Date(Date.now() + ttl * 1000).toISOString();

      const session: UnifiedSession = {
        id: '', // Will be set by database
        userId,
        sessionType,
        status: 'active',
        data,
        metadata: {
          ...metadata,
          lastActivity: now,
        },
        createdAt: now,
        expiresAt,
        updatedAt: now,
      };

      // Store in database first to get the generated ID
      const storedSession = await this.storeInDatabase(session);

      // Then store in Redis with the actual ID
      await this.storeInRedis(storedSession, ttl);

      logger.info('Unified session created', {
        sessionId: storedSession.id,
        userId,
        sessionType,
        expiresAt,
      });

      return storedSession;
    } catch (error) {
      logger.error('Failed to create unified session', { error, userId, sessionType });
      throw createError.internalServerError('Failed to create session');
    }
  }

  /**
   * Get session by ID with fallback strategy
   */
  async getSession(sessionId: string, userId?: string): Promise<UnifiedSession | null> {
    try {
      // Try Redis first for performance
      let session = await this.getFromRedis(sessionId);
      
      if (!session) {
        // Fallback to database
        session = await this.getFromDatabase(sessionId, userId);
        
        if (session) {
          // Restore to Redis for future requests
          const ttl = Math.floor((new Date(session.expiresAt).getTime() - Date.now()) / 1000);
          if (ttl > 0) {
            await this.storeInRedis(session, ttl);
          }
        }
      }

      if (!session) {
        return null;
      }

      // Check if session is expired
      if (new Date(session.expiresAt) < new Date()) {
        await this.expireSession(sessionId);
        return null;
      }

      // Verify user ownership if userId provided
      if (userId && session.userId !== userId) {
        logger.warn('Session access denied - user mismatch', { sessionId, userId, sessionUserId: session.userId });
        return null;
      }

      return session;
    } catch (error) {
      logger.error('Failed to get session', { error, sessionId, userId });
      return null;
    }
  }

  /**
   * Update session data and metadata
   */
  async updateSession(
    sessionId: string,
    updates: {
      data?: Partial<Record<string, any>>;
      metadata?: Partial<UnifiedSession['metadata']>;
      status?: UnifiedSession['status'];
    },
    userId?: string
  ): Promise<boolean> {
    try {
      const session = await this.getSession(sessionId, userId);
      if (!session) {
        return false;
      }

      const now = new Date().toISOString();
      const updatedSession: UnifiedSession = {
        ...session,
        data: updates.data ? { ...session.data, ...updates.data } : session.data,
        metadata: updates.metadata ? { ...session.metadata, ...updates.metadata, lastActivity: now } : { ...session.metadata, lastActivity: now },
        status: updates.status || session.status,
        updatedAt: now,
      };

      // Update in both storage layers
      await Promise.allSettled([
        this.storeInDatabase(updatedSession),
        this.storeInRedis(updatedSession, Math.floor((new Date(updatedSession.expiresAt).getTime() - Date.now()) / 1000)),
      ]);

      logger.info('Session updated', { sessionId, userId, updates });
      return true;
    } catch (error) {
      logger.error('Failed to update session', { error, sessionId, userId });
      return false;
    }
  }

  /**
   * Extend session expiration
   */
  async extendSession(sessionId: string, additionalTtl: number, userId?: string): Promise<boolean> {
    try {
      const session = await this.getSession(sessionId, userId);
      if (!session) {
        return false;
      }

      const newExpiresAt = new Date(Date.now() + additionalTtl * 1000).toISOString();
      const updatedSession: UnifiedSession = {
        ...session,
        expiresAt: newExpiresAt,
        updatedAt: new Date().toISOString(),
      };

      await Promise.allSettled([
        this.storeInDatabase(updatedSession),
        this.storeInRedis(updatedSession, additionalTtl),
      ]);

      logger.info('Session extended', { sessionId, userId, newExpiresAt });
      return true;
    } catch (error) {
      logger.error('Failed to extend session', { error, sessionId, userId });
      return false;
    }
  }

  /**
   * Expire/invalidate session
   */
  async expireSession(sessionId: string, userId?: string): Promise<boolean> {
    try {
      const session = await this.getSession(sessionId, userId);
      if (!session) {
        return false;
      }

      // Mark as expired in database
      await this.updateSession(sessionId, { status: 'expired' }, userId);

      // Remove from Redis
      await this.removeFromRedis(sessionId);

      logger.info('Session expired', { sessionId, userId });
      return true;
    } catch (error) {
      logger.error('Failed to expire session', { error, sessionId, userId });
      return false;
    }
  }

  /**
   * Get user's active sessions
   */
  async getUserSessions(
    userId: string,
    sessionType?: UnifiedSession['sessionType'],
    status: UnifiedSession['status'] = 'active'
  ): Promise<UnifiedSession[]> {
    try {
      let query = supabase
        .from('unified_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('status', status)
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false });

      if (sessionType) {
        query = query.eq('session_type', sessionType);
      }

      const { data: sessions, error } = await query;

      if (error) {
        logger.error('Failed to get user sessions from database', { error, userId });
        return [];
      }

      return sessions?.map(this.mapSessionFromDB) || [];
    } catch (error) {
      logger.error('Failed to get user sessions', { error, userId });
      return [];
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const { data, error } = await supabase
        .from('unified_sessions')
        .delete()
        .lt('expires_at', new Date().toISOString())
        .select('id');

      if (error) {
        logger.error('Failed to cleanup expired sessions', { error });
        return 0;
      }

      const cleanedCount = data?.length || 0;
      logger.info('Cleaned up expired sessions', { count: cleanedCount });
      return cleanedCount;
    } catch (error) {
      logger.error('Failed to cleanup expired sessions', { error });
      return 0;
    }
  }

  // Private helper methods
  private async storeInDatabase(session: UnifiedSession): Promise<UnifiedSession> {
    const dbSession = {
      // Let database generate UUID for id if not provided
      ...(session.id ? { id: session.id } : {}),
      user_id: session.userId,
      session_type: session.sessionType,
      status: session.status,
      data: session.data,
      metadata: session.metadata,
      expires_at: session.expiresAt,
      updated_at: session.updatedAt,
    };

    const { data, error } = await supabaseAdmin
      .from('unified_sessions')
      .upsert(dbSession, { onConflict: 'id' })
      .select()
      .single();

    if (error) {
      logger.error('Failed to store session in database', { error, sessionId: session.id });
      throw error;
    }

    // Return the session with the database-generated ID
    return {
      ...session,
      id: data.id,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };
  }

  private async storeInRedis(session: UnifiedSession, ttl: number): Promise<void> {
    try {
      await sessionHelpers.setSession(session.id, session.userId, session, ttl);
    } catch (redisError) {
      // Fallback to in-memory storage
      logger.debug('Redis unavailable, using fallback session store', { sessionId: session.id });
      await fallbackSessionHelpers.setSession(session.id, session.userId, session, ttl);
    }
  }

  private async getFromRedis(sessionId: string): Promise<UnifiedSession | null> {
    try {
      const sessionData = await sessionHelpers.getSession(sessionId);
      return sessionData?.data as UnifiedSession || null;
    } catch (redisError) {
      // Fallback to in-memory storage
      const sessionData = await fallbackSessionHelpers.getSession(sessionId);
      return sessionData?.data as UnifiedSession || null;
    }
  }

  private async removeFromRedis(sessionId: string): Promise<void> {
    try {
      await sessionHelpers.deleteSession(sessionId);
    } catch (redisError) {
      // Try fallback storage
      // Note: fallbackSessionHelpers doesn't have a delete method, so we'll skip this
      logger.debug('Could not remove from Redis fallback storage', { sessionId });
    }
  }

  private async getFromDatabase(sessionId: string, userId?: string): Promise<UnifiedSession | null> {
    let query = supabase
      .from('unified_sessions')
      .select('*')
      .eq('id', sessionId);

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data: session, error } = await query.single();

    if (error || !session) {
      return null;
    }

    return this.mapSessionFromDB(session);
  }

  private mapSessionFromDB(dbSession: any): UnifiedSession {
    return {
      id: dbSession.id,
      userId: dbSession.user_id,
      sessionType: dbSession.session_type,
      status: dbSession.status,
      data: dbSession.data || {},
      metadata: dbSession.metadata || {},
      createdAt: dbSession.created_at,
      expiresAt: dbSession.expires_at,
      updatedAt: dbSession.updated_at,
    };
  }
}

// Export singleton instance
export const unifiedSessionManager = new UnifiedSessionManager();

// Helper functions for AI session management
export const aiSessionHelpers = {
  /**
   * Create AI conversation session
   */
  async createAISession(
    userId: string,
    sessionData: Partial<AISessionData>,
    metadata: UnifiedSession['metadata'] = {}
  ): Promise<UnifiedSession> {
    const aiData: AISessionData = {
      sessionType: 'onboarding',
      userRole: 'CLIENT',
      language: 'ar',
      currentStep: 'welcome',
      messages: [],
      extractedData: {},
      recommendations: [],
      ...sessionData,
    };

    return unifiedSessionManager.createSession(
      userId,
      'ai_conversation',
      aiData,
      {
        userRole: aiData.userRole,
        language: aiData.language,
        ...metadata,
      }
    );
  },

  /**
   * Update AI session with new message
   */
  async addMessage(
    sessionId: string,
    message: AISessionData['messages'][0],
    userId?: string
  ): Promise<boolean> {
    const session = await unifiedSessionManager.getSession(sessionId, userId);
    if (!session || session.sessionType !== 'ai_conversation') {
      return false;
    }

    const aiData = session.data as AISessionData;
    const updatedMessages = [...aiData.messages, message];

    return unifiedSessionManager.updateSession(sessionId, {
      data: { ...aiData, messages: updatedMessages },
    }, userId);
  },

  /**
   * Update extracted data
   */
  async updateExtractedData(
    sessionId: string,
    extractedData: Partial<Record<string, any>>,
    userId?: string
  ): Promise<boolean> {
    const session = await unifiedSessionManager.getSession(sessionId, userId);
    if (!session || session.sessionType !== 'ai_conversation') {
      return false;
    }

    const aiData = session.data as AISessionData;
    const updatedExtractedData = { ...aiData.extractedData, ...extractedData };

    return unifiedSessionManager.updateSession(sessionId, {
      data: { ...aiData, extractedData: updatedExtractedData },
    }, userId);
  },
};
